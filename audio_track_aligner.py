#!/usr/bin/env python3

import os
import sys
import tempfile
import subprocess
import numpy as np
import librosa
import soundfile as sf
from scipy import signal
from scipy.io import wavfile
import shutil
from pathlib import Path
import logging
import argparse
from dataclasses import dataclass
from typing import List, Tuple, Dict


@dataclass
class SegmentMapping:
    """Class to store segment mapping between English and Dutch audio"""
    english_start: float  # in seconds
    english_end: float  # in seconds
    dutch_start: float  # in seconds
    dutch_end: float  # in seconds


class AudioAligner:
    """Class to align audio tracks from different language versions of the same content"""

    def __init__(self, english_video_path, dutch_video_path, output_path, temp_dir=None, debug=False):
        """Initialize the audio aligner with input and output paths"""
        self.english_video_path = Path(english_video_path)
        self.dutch_video_path = Path(dutch_video_path)
        self.output_path = Path(output_path)
        self.temp_dir = Path(temp_dir) if temp_dir else Path(tempfile.mkdtemp())
        self.english_audio_path = self.temp_dir / "english_audio.wav"
        self.dutch_audio_path = self.temp_dir / "dutch_audio.wav"
        self.aligned_dutch_path = self.temp_dir / "dutch_aligned.wav"

        # Setup logging
        level = logging.DEBUG if debug else logging.INFO
        logging.basicConfig(level=level, format="%(asctime)s - %(levelname)s - %(message)s")
        self.logger = logging.getLogger("AudioAligner")

        self.logger.info(f"Temp directory: {self.temp_dir}")

    def extract_audio(self, video_path: Path, output_path: Path):
        """Extract audio from video file using FFmpeg"""
        self.logger.info(f"Extracting audio from {video_path}")
        command = [
            "ffmpeg", "-y", "-i", str(video_path),
            "-vn",  # No video
            "-acodec", "pcm_s16le",  # PCM 16-bit
            "-ar", "44100",  # Sample rate
            "-ac", "1",  # Mono
            str(output_path)
        ]

        result = subprocess.run(command, capture_output=True, text=True)
        if result.returncode != 0:
            self.logger.error(f"FFmpeg error: {result.stderr}")
            raise RuntimeError(f"Failed to extract audio: {result.stderr}")

        return output_path

    def generate_fingerprints(self, audio_path: Path, chunk_size=10):
        """
        Generate audio fingerprints from file, focusing on non-speech elements
        Returns a list of fingerprints for each chunk of audio
        """
        self.logger.info(f"Generating fingerprints for {audio_path}")

        # Load audio
        y, sr = librosa.load(str(audio_path), sr=None)

        # Calculate duration in seconds
        duration = len(y) / sr
        chunks = int(np.ceil(duration / chunk_size))

        fingerprints = []

        for i in range(chunks):
            start_sample = i * chunk_size * sr
            end_sample = min(len(y), (i + 1) * chunk_size * sr)

            if end_sample - start_sample < sr:  # Skip chunks smaller than 1 second
                continue

            chunk = y[start_sample:end_sample]

            # Extract features focusing on non-speech elements

            # 1. Spectral contrast (good for music/effects detection)
            contrast = np.mean(librosa.feature.spectral_contrast(y=chunk, sr=sr), axis=1)

            # 2. RMS energy (overall energy levels)
            rms = np.mean(librosa.feature.rms(y=chunk), axis=1)[0]

            # 3. Zero crossing rate (helps distinguish music from speech)
            zcr = np.mean(librosa.feature.zero_crossing_rate(chunk))

            # 4. Spectral bandwidth
            bandwidth = np.mean(librosa.feature.spectral_bandwidth(y=chunk, sr=sr))

            # 5. Spectral rolloff
            rolloff = np.mean(librosa.feature.spectral_rolloff(y=chunk, sr=sr))

            # Create fingerprint as a concatenation of features
            fingerprint = np.concatenate([
                contrast,
                np.array([rms, zcr, bandwidth, rolloff])
            ])

            # Store fingerprint with its timestamp
            fingerprints.append({
                'start_time': start_sample / sr,
                'end_time': end_sample / sr,
                'fingerprint': fingerprint
            })

        self.logger.info(f"Generated {len(fingerprints)} fingerprints")
        return fingerprints

    def find_matching_points(self, eng_fingerprints, dutch_fingerprints, match_threshold=0.7):
        """
        Find matching points between English and Dutch audio using fingerprints
        Returns list of (english_time, dutch_time) pairs
        """
        self.logger.info("Finding matching points between audio tracks")

        matching_points = []

        # For each English fingerprint
        for eng_fp in eng_fingerprints:
            best_match = None
            best_score = -float('inf')

            # Compare to each Dutch fingerprint
            for dutch_fp in dutch_fingerprints:
                # Calculate cosine similarity
                eng_vec = eng_fp['fingerprint']
                dutch_vec = dutch_fp['fingerprint']

                if len(eng_vec) != len(dutch_vec):
                    continue

                # Normalize vectors
                eng_norm = np.linalg.norm(eng_vec)
                dutch_norm = np.linalg.norm(dutch_vec)

                if eng_norm == 0 or dutch_norm == 0:
                    continue

                similarity = np.dot(eng_vec, dutch_vec) / (eng_norm * dutch_norm)

                if similarity > best_score:
                    best_score = similarity
                    best_match = dutch_fp

            # If match is good enough, add to matching points
            if best_match and best_score > match_threshold:
                matching_points.append((
                    eng_fp['start_time'],
                    best_match['start_time']
                ))
                self.logger.debug(f"Match: English {eng_fp['start_time']:.2f}s -> Dutch {best_match['start_time']:.2f}s (score: {best_score:.4f})")

        self.logger.info(f"Found {len(matching_points)} initial matching points")

        # If we have very few matches, try with lower threshold
        if len(matching_points) < 3:
            self.logger.info(f"Too few matches with threshold {match_threshold}, trying with lower threshold 0.5")
            return self.find_matching_points(eng_fingerprints, dutch_fingerprints, match_threshold=0.5)

        # Filter out outliers (points that don't follow the general trend)
        if len(matching_points) > 5:
            # Calculate time offsets
            offsets = [e - d for e, d in matching_points]
            median_offset = np.median(offsets)
            mad = np.median([abs(o - median_offset) for o in offsets])

            # Keep points with offset close to median (more lenient filtering)
            filtered_points = [
                point for point, offset in zip(matching_points, offsets)
                if abs(offset - median_offset) < 5 * mad  # Increased from 3 to 5
            ]

            self.logger.info(f"Filtered matching points from {len(matching_points)} to {len(filtered_points)}")
            matching_points = filtered_points

        # Ensure we have start and end points
        if matching_points:
            # Add start point if not present
            first_point = matching_points[0]
            if first_point[0] > 10.0:  # If first match is more than 10 seconds in
                matching_points.insert(0, (0.0, 0.0))
                self.logger.info("Added start point (0.0, 0.0)")

        # Sort by English timestamp
        matching_points.sort(key=lambda x: x[0])

        return matching_points

    def create_segment_map(self, matching_points, min_segment_duration=1.0):
        """Create a mapping of segments between the two videos"""
        self.logger.info("Creating segment map")
        segments = []

        for i in range(len(matching_points) - 1):
            eng_start = matching_points[i][0]
            eng_end = matching_points[i+1][0]
            dutch_start = matching_points[i][1]
            dutch_end = matching_points[i+1][1]

            # Skip very short segments
            if eng_end - eng_start < min_segment_duration:
                continue

            segments.append(SegmentMapping(
                english_start=eng_start,
                english_end=eng_end,
                dutch_start=dutch_start,
                dutch_end=dutch_end
            ))

        self.logger.info(f"Created {len(segments)} segment mappings")
        return segments

    def process_audio_segments(self, segment_map):
        """Process Dutch audio into aligned segments based on the segment map"""
        self.logger.info("Processing audio segments")

        # Create a directory for segment files
        segment_dir = self.temp_dir / "segments"
        segment_dir.mkdir(exist_ok=True)

        # Create a file list for the concat filter
        file_list_path = segment_dir / "segments.txt"
        with open(file_list_path, "w") as file_list:

            for i, segment in enumerate(segment_map):
                segment_file = segment_dir / f"segment_{i:03d}.wav"

                # Extract segment from Dutch audio
                extract_cmd = [
                    "ffmpeg", "-y",
                    "-i", str(self.dutch_audio_path),
                    "-ss", str(segment.dutch_start),
                    "-to", str(segment.dutch_end),
                    "-c", "copy",
                    str(segment_file)
                ]

                subprocess.run(extract_cmd, capture_output=True)

                # Calculate durations
                eng_duration = segment.english_end - segment.english_start
                dutch_duration = segment.dutch_end - segment.dutch_start

                # Time stretch if durations don't match
                processed_file = segment_dir / f"processed_{i:03d}.wav"

                if abs(eng_duration - dutch_duration) > 0.1:  # More than 100ms difference
                    stretch_factor = eng_duration / dutch_duration

                    # Use FFmpeg for time stretching
                    stretch_cmd = [
                        "ffmpeg", "-y",
                        "-i", str(segment_file),
                        "-filter:a", f"atempo={stretch_factor}",
                        str(processed_file)
                    ]

                    subprocess.run(stretch_cmd, capture_output=True)
                else:
                    # Just copy the file
                    shutil.copy(segment_file, processed_file)

                # Add to file list
                file_list.write(f"file '{processed_file}'\n")

        # Concatenate all processed segments
        concat_cmd = [
            "ffmpeg", "-y",
            "-f", "concat",
            "-safe", "0",
            "-i", str(file_list_path),
            "-c", "copy",
            str(self.aligned_dutch_path)
        ]

        subprocess.run(concat_cmd, capture_output=True)

        return self.aligned_dutch_path

    def mux_audio_with_video(self):
        """Add the aligned Dutch audio to the English video"""
        self.logger.info("Muxing aligned Dutch audio with high-quality English video")

        mux_cmd = [
            "ffmpeg", "-y",
            "-i", str(self.english_video_path),
            "-i", str(self.aligned_dutch_path),
            "-map", "0:v",  # Video from first input
            "-map", "0:a",  # Audio from first input
            "-map", "1:a",  # Audio from second input
            "-c:v", "copy",  # Copy video codec
            "-c:a", "aac",  # Convert audio to AAC
            "-metadata:s:a:1", "language=dut",  # Set language for second audio
            "-metadata:s:a:1", "title=Dutch",   # Set title for second audio
            str(self.output_path)
        ]

        subprocess.run(mux_cmd, capture_output=True)

        return self.output_path

    def align(self):
        """Perform the entire alignment process"""
        try:
            # 1. Extract audio from both videos
            self.extract_audio(self.english_video_path, self.english_audio_path)
            self.extract_audio(self.dutch_video_path, self.dutch_audio_path)

            # 2. Generate fingerprints
            eng_fingerprints = self.generate_fingerprints(self.english_audio_path)
            dutch_fingerprints = self.generate_fingerprints(self.dutch_audio_path)

            # 3. Find matching points
            matching_points = self.find_matching_points(eng_fingerprints, dutch_fingerprints)

            if len(matching_points) < 2:
                self.logger.error("Insufficient matching points found")
                return False

            # 4. Create segment map
            segment_map = self.create_segment_map(matching_points)

            if not segment_map:
                self.logger.error("Failed to create segment map")
                return False

            # 5. Process audio segments
            self.process_audio_segments(segment_map)

            # 6. Mux aligned audio with video
            self.mux_audio_with_video()

            self.logger.info(f"Successfully created output video: {self.output_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error during alignment: {e}")
            return False

    def cleanup(self):
        """Clean up temporary files"""
        self.logger.info("Cleaning up temporary files")
        try:
            shutil.rmtree(self.temp_dir)
        except Exception as e:
            self.logger.error(f"Error cleaning up: {e}")


def main():
    """Main function to parse arguments and run the aligner"""
    parser = argparse.ArgumentParser(
        description="Align a Dutch audio track with a high-quality English video"
    )

    parser.add_argument("english_video", help="Path to high quality English video")
    parser.add_argument("dutch_video", help="Path to low quality Dutch video")
    parser.add_argument("output", help="Path to output video with both audio tracks")
    parser.add_argument(
        "--temp-dir",
        help="Directory for temporary files (default: system temp dir)"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )

    args = parser.parse_args()

    print("Audio Track Alignment Tool")
    print("--------------------------")
    print(f"English video: {args.english_video}")
    print(f"Dutch video:   {args.dutch_video}")
    print(f"Output:        {args.output}")
    print()

    # Create aligner and run it
    aligner = AudioAligner(
        args.english_video,
        args.dutch_video,
        args.output,
        args.temp_dir,
        args.debug
    )

    try:
        success = aligner.align()
        if success:
            print(f"Successfully created output video: {args.output}")
        else:
            print("Failed to align audio tracks")
            sys.exit(1)
    finally:
        aligner.cleanup()


if __name__ == "__main__":
    main()