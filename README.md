# Audio Offset Finder - DaVinci Resolve OTIO Generator

This application processes video files and their corresponding dub files to create OpenTimelineIO (OTIO) files that can be imported into DaVinci Resolve with proper clip linking and picture-in-picture effects for dub files.

## Features

- ✅ **Clip Linking**: Video and audio clips from the same source file are properly linked using DaVinci Resolve's `Link Group ID` metadata
- ✅ **Picture-in-Picture**: Dub videos appear as small overlays with Transform effects (zoom, pan, tilt)
- ✅ **Cut Detection**: Automatically detects cuts using silence detection or black frame detection
- ✅ **Audio Conversion**: Converts audio to WAV format for DaVinci Resolve compatibility
- ✅ **FPS Conversion**: Optional re-encoding of dub files with different frame rates
- ✅ **Modular Architecture**: Clean, organized code structure for easy maintenance

## File Structure

The application has been refactored into a modular structure:

### Core Modules

- **`main.py`** - Main application entry point and CLI interface
- **`config_manager.py`** - Configuration management and settings
- **`media_analyzer.py`** - Media analysis functions (silence/black frame detection)
- **`otio_builder.py`** - OTIO timeline and clip creation functions
- **`file_processor.py`** - File processing and conversion logic

### Utility Files

- **`test_linking.py`** - Test script to verify clip linking functionality
- **`audio_offset_finder.py`** - Deprecated wrapper for backward compatibility

### Configuration

- **`config.yml`** - Configuration file with settings like `dub_path`

## Usage

### Basic Usage

The application reads the directory to process from `config.yml` by default:

```bash
# Use the new modular implementation (reads directory from config.yml)
python main.py

# Or use the deprecated wrapper (shows warning)
python audio_offset_finder.py
```

### Command Line Options

```bash
python main.py [OPTIONS]

Options:
  --dir PATH          Directory to process (default: reads 'dub_path' from config.yml)
  --reencode          Enable re-encoding options for dub files
  --silence           Use silence detection instead of black frame detection
  --dub-convert       Convert dub file to different FPS
  --mute-original     Mute the audio track of the original file
```

### Examples

```bash
# Process files using directory from config.yml
python main.py

# Override config.yml directory
python main.py --dir /path/to/videos

# Enable re-encoding with silence detection (uses config.yml directory)
python main.py --reencode --silence

# Process with custom directory and re-encoding
python main.py --dir "C:\Videos\MyProject" --reencode
```

## Configuration

**The application reads the directory from `config.yml` by default.** Edit this file to set your working directory:

```yaml
dub_path: C:\Users\<USER>\Videos\Rocket Power\test\
```

If the config file doesn't exist, the application will use the current working directory as default.

## Testing Clip Linking

Use the test script to verify that clip linking is working correctly:

```bash
python test_linking.py path/to/your/output.otio
```

This will analyze the OTIO file and report:
- All tracks and clips with their Link Group IDs
- Verification that corresponding video and audio clips have matching Link Group IDs
- Summary of linking status

## How It Works

### 1. File Detection
- Scans directory for video files (.mkv, .mp4)
- Matches original files with corresponding dub files using PTN parsing
- Skips files that already have OTIO outputs

### 2. Cut Detection
- **Black Frame Detection** (default): Analyzes video for black frames to identify cuts
- **Silence Detection** (optional): Analyzes audio for silence to identify cuts

### 3. Audio Processing
- Extracts audio from video files and converts to WAV format
- Handles multiple audio streams in dub files (EAC3, AAC)

### 4. OTIO Generation
- Creates video and audio tracks with proper cuts
- Applies Transform effects to dub video tracks for picture-in-picture display
- Sets `Link Group ID` metadata for clip linking in DaVinci Resolve

### 5. Timeline Structure
The generated timeline contains:
1. **Original Video Track** (full screen)
2. **Dub Video Track** (picture-in-picture overlay with Transform effect)
3. **Original Audio Track**
4. **Dub Audio Track**

## Transform Effect Parameters

Dub videos are automatically transformed with these parameters:
- **Zoom X/Y**: 0.31 (31% of original size)
- **Pan**: 0.232 (horizontal positioning)
- **Tilt**: -0.278 (vertical positioning)

This creates a small picture-in-picture overlay in the corner of the frame.

## Requirements

- Python 3.7+
- FFmpeg (for media analysis and conversion)
- Required Python packages (see `requirements.txt`)

## Dependencies

- `opentimelineio` - OTIO file creation
- `PTN` - Filename parsing
- `questionary` - Interactive CLI prompts
- `pymediainfo` - Media file analysis
- `pyyaml` - Configuration file parsing

## Troubleshooting

### Linking Not Working
- Ensure you're using the correct metadata structure (`Link Group ID`)
- Test with the `test_linking.py` script
- Check that clips have matching Link Group ID values

### Transform Effect Not Applied
- Verify that dub files contain "dub" in their filename
- Check that the Transform effect is properly applied to dub video clips

### Audio Conversion Issues
- Ensure FFmpeg is installed and accessible
- Check that audio streams are being detected correctly
- Verify WAV files are being created

## Migration from Old Version

The old monolithic `audio_offset_finder.py` has been replaced with a modular structure. The old file is maintained for backward compatibility but shows a deprecation warning.

**Recommended migration:**
1. Update your scripts to use `python main.py` instead of `python audio_offset_finder.py`
2. The command-line interface remains the same
3. All functionality is preserved in the new modular structure
