"""
File processing and conversion logic.
"""
import os
import re
import shutil
import subprocess
from pathlib import Path
from typing import List, Dict, Any, <PERSON><PERSON>, Optional

import PTN
from opentimelineio._otio import Track
from pymediainfo import MediaInfo

from media_analyzer import (
    detect_silences, detect_blackframes, get_video_fps,
    get_video_frame_count, extract_audio_to_wav,
    extract_audio_stream_to_wav, get_audio_streams,
    get_audio_duration, create_silent_audio
)
from otio_builder import create_tracks_from_cuts


def generate_cuts_from_matches(matches: List[Tuple], frames_count: int, 
                              link_index: int) -> Tuple[List[Dict[str, Any]], int]:
    """
    Generate cut information from detection matches.
    
    Args:
        matches: List of detection matches (silences or black frames)
        frames_count: Total frame count of the video
        link_index: Starting link index
        
    Returns:
        Tuple of (cuts_list, updated_link_index)
    """
    index = 0
    previous_cut = 0
    cuts = []
    current_link_index = link_index
    
    for match in matches:
        if len(matches) > 1 and index == 0:
            if matches[1][0] != "1":
                index += 1
                continue
        if len(matches) > 1 and match != matches[-1]:
            if int(int(matches[index + 1][0])) - int(match[0]) == 1:
                index += 1
                continue
        
        new_cut = {
            "cut_in": previous_cut,
            "cut_out": float(match[0]),
            "cut_duration": float(match[0]) - previous_cut,
            "link_id": current_link_index
        }
        cuts.append(new_cut)
        previous_cut = float(match[0])
        index += 1
        current_link_index += 1
    
    # Add final cut
    last_cut = {
        "cut_in": previous_cut,
        "cut_out": float(frames_count),
        "cut_duration": float(frames_count) - previous_cut,
        "link_id": current_link_index
    }
    current_link_index += 1
    cuts.append(last_cut)
    
    return cuts, current_link_index


def process_file_cuts(directory: bytes, file: str, audio_file: str, 
                     processed_files: Dict[str, Any], silence: bool = False, 
                     link_index: int = 0) -> Tuple[Track, Track, int]:
    """
    Process a file and generate OTIO tracks with cuts.
    
    Args:
        directory: Directory path as bytes
        file: Video file name
        audio_file: Audio file name
        processed_files: Cache of already processed files
        silence: Whether to use silence detection instead of black frame detection
        link_index: Starting link index for clips
        
    Returns:
        Tuple of (video_track, audio_track, updated_link_index)
    """
    full_path = f'{directory.decode("utf-8")}/{file}'
    audio_full_path = f'{directory.decode("utf-8")}/{audio_file}'
    
    # Get video properties
    fps = get_video_fps(full_path)
    frames_count = get_video_frame_count(full_path)
    
    # Detect cuts based on silences or black frames
    if silence:
        if file in processed_files:
            matches, duration = processed_files[file]
        else:
            matches, duration = detect_silences(full_path)
            processed_files[file] = matches, duration
    else:
        if file in processed_files:
            matches, duration = processed_files[file]
        else:
            matches, duration = detect_blackframes(full_path)
            processed_files[file] = matches, duration
    
    # Generate cuts from matches
    cuts, updated_link_index = generate_cuts_from_matches(matches, frames_count, link_index)
    
    # Determine if this is a dub file
    is_dub = "dub" in file.lower()
    
    # Create tracks
    video_track, audio_track = create_tracks_from_cuts(
        full_path, audio_full_path, file, audio_file, cuts, fps, is_dub
    )
    
    return video_track, audio_track, updated_link_index


def find_matching_dub_file(filename: str, directory: bytes) -> Optional[str]:
    """
    Find the matching dub file for a given original file.
    
    Args:
        filename: Original filename
        directory: Directory to search in
        
    Returns:
        Matching dub filename or None if not found
    """
    try:
        file_info = PTN.parse(filename)
    except Exception:
        print(f"[error] Error parsing file with PTN ({filename})")
        return None
    
    for dub_file in os.listdir(directory):
        temp_filename = os.fsdecode(dub_file)
        if ("._" not in temp_filename and "dub" in temp_filename and 
            "v2" not in temp_filename and "wav" not in temp_filename):
            try:
                temp_file_info = PTN.parse(temp_filename)
                
                # Handle episode lists
                if (isinstance(temp_file_info.get('episode'), list) and 
                    len(temp_file_info['episode']) > 2):
                    temp_file_info['episode'] = [
                        temp_file_info['episode'][0], 
                        temp_file_info['episode'][-1]
                    ]
                
                # Check if episodes match
                if (file_info.get('episode') == temp_file_info.get('episode') or
                    (isinstance(temp_file_info.get('episode'), list) and 
                     file_info.get('episode') in temp_file_info['episode']) or
                    (isinstance(file_info.get('episode'), list) and 
                     temp_file_info.get('episode') in file_info['episode'])):
                    return temp_filename
                    
            except Exception:
                continue
    
    return None


def should_process_file(filename: str, args_dir: str) -> bool:
    """
    Check if a file should be processed.
    
    Args:
        filename: Name of the file
        args_dir: Directory path
        
    Returns:
        True if file should be processed
    """
    basename = os.path.splitext(filename)[0]
    
    return (
        (filename.endswith(".mkv") or filename.endswith(".mp4")) and
        "._" not in filename and
        "dub" not in filename and
        not os.path.exists(f'{args_dir}/{basename}.otio')
    )


def prepare_audio_files(org_file: str, dub_file: str, args_dir: str) -> Tuple[str, str]:
    """
    Prepare audio files for processing (convert to WAV if needed).
    
    Args:
        org_file: Original video file path
        dub_file: Dub video file path
        args_dir: Working directory
        
    Returns:
        Tuple of (original_audio_filename, dub_audio_filename)
    """
    # Convert original audio to WAV
    output_wav = os.path.join(args_dir, f"{Path(org_file).stem}.wav")
    extract_audio_to_wav(org_file, output_wav)
    org_audio_filename = f'{Path(org_file).stem}.wav'
    
    # Find and convert dub audio to WAV
    audio_streams = get_audio_streams(dub_file)
    dub_audio_filename = f'{Path(dub_file).stem}.wav'
    
    for stream in audio_streams:
        if "eac3" in stream or "aac" in stream:
            # Convert the audio stream to wav using ffmpeg
            audio_stream_index = re.search(r'Stream #\d+:(\d+)', stream).group(1)
            dub_audio_filename = f'{Path(dub_file).stem}.{audio_stream_index}.wav'
            extract_audio_stream_to_wav(
                dub_file, 
                f'{args_dir}/{dub_audio_filename}',
                audio_stream_index
            )
            break
    
    return org_audio_filename, dub_audio_filename


def reencode_dub_file(dub_file: str, dub_file_speed: List[str],
                     dub_output_fps: List[str], args_dir: str) -> None:
    """
    Re-encode dub file with different FPS if needed.

    Args:
        dub_file: Path to dub file
        dub_file_speed: Speed conversion parameters
        dub_output_fps: Output FPS parameters
        args_dir: Working directory
    """
    if dub_file_speed == "1.000":
        return

    basename = f'{Path(dub_file).stem}'
    media_info = MediaInfo.parse(dub_file,
                                library_file="/opt/homebrew/Cellar/libmediainfo/25.04/lib/libmediainfo.dylib")

    if len(dub_file_speed) == 2:
        fps_changes = [[float(dub_file_speed[0]), float(dub_file_speed[1]), float(dub_output_fps[0])]]
    else:
        fps_changes = [
            [float(dub_file_speed[0]), float(dub_file_speed[1]), float(dub_output_fps[0])],
            [float(dub_file_speed[2]), float(dub_file_speed[3]), float(dub_output_fps[1])]
        ]

    for fps_change in fps_changes:
        # Creating the tempo for audio conversion based on to and from FPS
        tempo = fps_change[0] / fps_change[1]

        # Converting all audio tracks we have found
        for audio_track in media_info.audio_tracks:
            print(f"\n[info] Re-encoding audio #{audio_track.stream_identifier}")
            command = [
                shutil.which("ffmpeg"), '-hide_banner', '-loglevel', 'error',
                '-i', dub_file, '-map', f'0:a:{audio_track.stream_identifier}',
                '-filter:a', f"atempo={tempo}", '-vn',
                f'{args_dir}/{basename}.{audio_track.stream_identifier}.wav'
            ]
            subprocess.run(command)

        # Re-encode the video with new FPS, also SD version will do fine for this
        print(f"\n[info] Re-encoding video with new FPS")
        subprocess.run([
            shutil.which("ffmpeg"), '-hide_banner', '-loglevel', 'error',
            '-i', dub_file, "-r", str(fps_change[2]), "-an",
            "-filter:v", f"setpts={str(fps_change[0] / fps_change[1])}*PTS,scale=720:480",
            f"{args_dir}/{basename}.v2.mkv"
        ])

        # Removing original dub file since we re-encoded it
        os.remove(dub_file)

        # Start of command to recreate the dub file with re-encoded video and audio
        print(f"\n[info] Re-muxing into new MKV container")
        command = [shutil.which("mkvmerge"), '-o', dub_file, f"{args_dir}/{basename}.v2.mkv"]

        # Add the speed-up audio track
        for audio_track in media_info.audio_tracks:
            # Set language of track
            language = audio_track.language if audio_track.language else "en"
            delay = audio_track.delay if audio_track.delay is not None else 0

            command += ["--language", f"0:{language}", "--sync", f"0:{delay}"]

            # Add title to track if set
            if audio_track.title:
                command += ["--track-name", f"0:{audio_track.title}"]

            command += [f"{args_dir}/{basename}.{audio_track.stream_identifier}.wav"]

        subprocess.run(command)

        # Remove unwanted files
        for audio_track in media_info.audio_tracks:
            os.remove(f'{args_dir}/{basename}.v2.mkv')
            os.remove(f'{args_dir}/{basename}.{audio_track.stream_identifier}.wav')
