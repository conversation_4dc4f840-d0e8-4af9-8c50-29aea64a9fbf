#!/usr/bin/env python3
"""
Main application for creating DaVinci Resolve OTIO files using audio track alignment.

This application uses the AudioAligner to align Dutch and English audio tracks,
then creates OpenTimelineIO (OTIO) files based on the segment mappings from the alignment.

<AUTHOR>
@version 1.0
"""
import os
import tempfile
from argparse import ArgumentParser
from pathlib import Path
from typing import List, Dict, Any, Tuple

import questionary
from questionary import Choice

from config_manager import config_manager
from file_processor import find_matching_dub_file, should_process_file, prepare_audio_files, reencode_dub_file
from otio_builder import create_timeline, save_timeline, create_tracks_from_cuts
from audio_track_aligner import AudioAligner, SegmentMapping
from media_analyzer import get_video_fps, get_video_frame_count


def get_speed_choices():
    """Get the available speed conversion choices."""
    return [
        Choice(title="No", value="1.000"),
        Choice(title="Re-encode 25", value=["25", "25"]),
        Choice(title="23.976 -> 25", value=["23.976", "25"]),
        Choice(title="23.976 -> 23.950", value=["23.976", "23.950"]),
        Choice(title="24 -> 23.875", value=["24", "23.875"]),
        Choice(title="25 -> 23.976", value=["25", "23.976"]),
        Choice(title="24 -> 23.976", value=["24", "23.976"]),
        Choice(title="50 -> 49.95", value=["50", "49.95"]),
        Choice(title="49.95 -> 50", value=["49.95", "50"]),
        Choice(title="49.95 -> 50 -> 23.950", value=["49.95", "50", "23.976", "23.950"])
    ]


def get_fps_choices():
    """Get the available FPS choices."""
    return ["23.976", "24", "25", "29.97", "30", "50"]


def get_user_preferences(args):
    """Get user preferences for re-encoding."""
    speed_choices = get_speed_choices()
    fps_choices = get_fps_choices()
    
    # Asking the user if they wish to change FPS of the dub files
    if args.reencode:
        dub_file_speed = questionary.select(
            "You want to do speed up the dub files?", 
            choices=speed_choices
        ).ask()
    else:
        dub_file_speed = "1.000"
    
    # If user wants this, give options for output FPS
    dub_output_fps = []
    dub_output_fps_0 = (questionary.select(
        "What should be the output fps of dub file?", 
        choices=fps_choices
    ).skip_if(dub_file_speed == "1.000" or len(dub_file_speed) > 2).ask())
    
    dub_output_fps_1 = (questionary.select(
        "What should be the output fps of first round?", 
        choices=fps_choices
    ).skip_if(dub_file_speed == "1.000" or len(dub_file_speed) == 2).ask())
    
    dub_output_fps_2 = (questionary.select(
        "What should be the output fps of second round?", 
        choices=fps_choices
    ).skip_if(dub_file_speed == "1.000" or len(dub_file_speed) == 2).ask())
    
    if dub_output_fps_0 is not None:
        dub_output_fps.append(dub_output_fps_0)
    if dub_output_fps_2 is not None:
        dub_output_fps.append(dub_output_fps_1)
        dub_output_fps.append(dub_output_fps_2)
    
    return dub_file_speed, dub_output_fps


def convert_segments_to_cuts(segment_map: List[SegmentMapping], fps: float, 
                           link_index: int, is_english: bool = True) -> Tuple[List[Dict[str, Any]], int]:
    """
    Convert segment mappings from audio aligner to cut dictionaries for OTIO.
    
    Args:
        segment_map: List of SegmentMapping objects from audio aligner
        fps: Frame rate of the video
        link_index: Starting link index
        is_english: Whether to use English timings (True) or Dutch timings (False)
        
    Returns:
        Tuple of (cuts_list, updated_link_index)
    """
    cuts = []
    current_link_index = link_index
    
    for segment in segment_map:
        if is_english:
            start_time = segment.english_start
            end_time = segment.english_end
        else:
            start_time = segment.dutch_start
            end_time = segment.dutch_end
        
        # Convert seconds to frames
        cut_in_frames = int(start_time * fps)
        cut_out_frames = int(end_time * fps)
        duration_frames = cut_out_frames - cut_in_frames
        
        cut = {
            "cut_in": cut_in_frames,
            "cut_out": cut_out_frames,
            "cut_duration": duration_frames,
            "link_id": current_link_index
        }
        cuts.append(cut)
        current_link_index += 1
    
    return cuts, current_link_index


def process_file_pair_with_aligner(filename: str, dub_filename: str, args_dir: str, 
                                 dub_file_speed, dub_output_fps):
    """
    Process a pair of original and dub files using audio track alignment.
    
    Args:
        filename: Original filename
        dub_filename: Dub filename
        args_dir: Working directory
        dub_file_speed: Speed conversion parameters
        dub_output_fps: Output FPS parameters
    """
    print(f"[info] Processing {filename} with dub {dub_filename} using audio alignment")
    
    dub_file = f'{args_dir}/{dub_filename}'
    org_file = f'{args_dir}/{filename}'
    
    # Prepare audio files
    org_audio_filename, dub_audio_filename = prepare_audio_files(
        org_file, dub_file, args_dir
    )
    
    # Re-encode dub file if needed
    reencode_dub_file(dub_file, dub_file_speed, dub_output_fps, args_dir)
    
    print("[info] Running audio track alignment...")
    
    # Create temporary directory for alignment
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create audio aligner
        aligner = AudioAligner(
            english_video_path=org_file,
            dutch_video_path=dub_file,
            output_path=os.path.join(temp_dir, "aligned_output.mkv"),
            temp_dir=temp_dir,
            debug=False
        )
        
        try:
            # Extract audio from both videos
            aligner.extract_audio(aligner.english_video_path, aligner.english_audio_path)
            aligner.extract_audio(aligner.dutch_video_path, aligner.dutch_audio_path)
            
            # Generate fingerprints
            eng_fingerprints = aligner.generate_fingerprints(aligner.english_audio_path)
            dutch_fingerprints = aligner.generate_fingerprints(aligner.dutch_audio_path)
            
            # Find matching points
            matching_points = aligner.find_matching_points(eng_fingerprints, dutch_fingerprints)
            
            if len(matching_points) < 2:
                print("[error] Insufficient matching points found for alignment")
                return
            
            # Create segment map
            segment_map = aligner.create_segment_map(matching_points)
            
            if not segment_map:
                print("[error] Failed to create segment map")
                return
            
            print(f"[info] Created {len(segment_map)} aligned segments")
            
        except Exception as e:
            print(f"[error] Audio alignment failed: {e}")
            return
    
    print("[info] Processing into DaVinci OTIO file...")
    
    # Get video properties
    fps = get_video_fps(org_file)
    
    # Convert segment mappings to cuts
    link_index = 0
    
    # Create cuts for original file (using English timings)
    orig_cuts, link_index = convert_segments_to_cuts(segment_map, fps, link_index, is_english=True)
    
    # Create cuts for dub file (using Dutch timings)
    dub_cuts, link_index = convert_segments_to_cuts(segment_map, fps, link_index, is_english=False)
    
    # Create tracks using existing OTIO builder functions
    org_audio_full_path = f'{args_dir}/{org_audio_filename}'
    dub_audio_full_path = f'{args_dir}/{dub_audio_filename}'
    
    # Create original tracks
    orig_video_track, orig_audio_track = create_tracks_from_cuts(
        org_file, org_audio_full_path, filename, org_audio_filename, 
        orig_cuts, fps, is_dub=False
    )
    
    # Create dub tracks (with Transform effect)
    dub_video_track, dub_audio_track = create_tracks_from_cuts(
        dub_file, dub_audio_full_path, dub_filename, dub_audio_filename, 
        dub_cuts, fps, is_dub=True
    )
    
    # Create timeline with all tracks
    timeline = create_timeline(filename, [
        orig_video_track,
        dub_video_track, 
        orig_audio_track,
        dub_audio_track
    ])
    
    # Save to OTIO file
    otio_path = os.path.join(args_dir, f"{os.path.splitext(filename)[0]}_aligned.otio")
    save_timeline(timeline, otio_path)


def run(args):
    """Main application logic."""
    print("[info] Audio Offset Finder - DaVinci Resolve OTIO Generator (Audio Alignment Version)")
    print("=" * 80)

    # Getting directory from config.yml or from command line arguments
    if args.dir is not None:
        args.dir = args.dir.replace("\\", "/")
        print(f"[info] Using directory from command line: {args.dir}")
    else:
        args.dir = config_manager.get_dub_path()
        print(f"[info] Using directory from config.yml: {args.dir}")

    # Verify directory exists
    if not os.path.exists(args.dir):
        print(f"[error] Directory does not exist: {args.dir}")
        print("[info] Please check your config.yml file or specify a valid directory with --dir")
        return

    directory = os.fsencode(args.dir)
    
    # Get user preferences
    dub_file_speed, dub_output_fps = get_user_preferences(args)
    
    # Process all files in directory
    for file in os.listdir(directory):
        filename = os.fsdecode(file)
        
        # Check if file should be processed
        if not should_process_file(filename, args.dir):
            continue
        
        # Find matching dub file
        dub_filename = find_matching_dub_file(filename, directory)
        if not dub_filename:
            print(f"[info] No dub file found for {filename}")
            continue
        
        try:
            process_file_pair_with_aligner(
                filename, dub_filename, args.dir, 
                dub_file_speed, dub_output_fps
            )
        except Exception as e:
            print(f"[error] Error processing {filename}: {e}")
            continue


def main():
    """Entry point for the application."""
    parser = ArgumentParser(
        description="Create DaVinci Resolve OTIO files using audio track alignment"
    )
    parser.add_argument(
        "--dir",
        help="Directory to process (default: reads 'dub_path' from config.yml)",
        required=False, type=str, default=None
    )
    parser.add_argument(
        "--reencode", 
        action='store_true',
        help="Enable re-encoding options for dub files"
    )
    parser.add_argument(
        "--dub-convert", 
        help="Convert dub file to different FPS", 
        action='store_true'
    )
    
    args = parser.parse_args()
    
    try:
        run(args)
    except KeyboardInterrupt:
        print("\n[info] Operation cancelled by user")
    except Exception as e:
        print(f"[error] Unexpected error: {e}")


if __name__ == '__main__':
    main()
