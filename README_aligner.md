# Audio Track Aligner OTIO Generator

This is a new version of the main OTIO generator that uses audio track alignment instead of silence/black frame detection to create cut segments.

## Key Differences from Original main.py

### Original Approach (main.py)
- Uses **silence detection** or **black frame detection** to find cut points
- Detects gaps/breaks in audio or video to determine where to split content
- Works well when there are clear silence periods or black frames between segments

### New Approach (main_aligner.py)
- Uses **audio fingerprinting and alignment** to match segments between English and Dutch tracks
- Analyzes audio features (spectral contrast, RMS energy, zero crossing rate, etc.) to find matching content
- Creates segment mappings based on audio similarity rather than gaps
- More sophisticated approach that can handle content without clear breaks

## How It Works

1. **Audio Extraction**: Extracts audio from both English (original) and Dutch (dub) video files
2. **Fingerprint Generation**: Creates audio fingerprints for chunks of audio using:
   - Spectral contrast (good for music/effects detection)
   - RMS energy (overall energy levels)
   - Zero crossing rate (helps distinguish music from speech)
   - Spectral bandwidth and rolloff
3. **Matching**: Finds matching points between English and Dutch audio using cosine similarity
4. **Segment Mapping**: Creates time-aligned segments between the two tracks
5. **OTIO Generation**: Converts segment mappings to OTIO cuts and creates timeline

## Usage

```bash
# Basic usage (reads directory from config.yml)
python main_aligner.py

# Specify directory
python main_aligner.py --dir "C:/path/to/your/videos"

# With re-encoding options
python main_aligner.py --reencode

# With FPS conversion
python main_aligner.py --dub-convert
```

## Output

- Creates OTIO files with `_aligned.otio` suffix to distinguish from original method
- Maintains the same track structure as original:
  - Original video track
  - Dub video track (with Transform effect for picture-in-picture)
  - Original audio track
  - Dub audio track
- Preserves DaVinci Resolve linking metadata

## When to Use Each Approach

### Use Original main.py when:
- Your content has clear silence periods or black frames between segments
- You want to split based on actual gaps in content
- Processing content with natural breaks (commercials, scene changes, etc.)

### Use main_aligner.py when:
- Your content doesn't have clear silence/black frame breaks
- You want to align similar content between different language versions
- The English and Dutch versions have different timing but similar content structure
- You need more sophisticated content-based alignment

## Requirements

Both scripts use the same dependencies, but the aligner version additionally leverages:
- `librosa` for audio analysis
- `numpy` and `scipy` for signal processing
- The existing `audio_track_aligner.py` module

## Technical Details

### Segment Conversion
The aligner converts `SegmentMapping` objects to cut dictionaries:

```python
SegmentMapping(
    english_start=10.5,    # seconds
    english_end=25.3,      # seconds  
    dutch_start=12.1,      # seconds
    dutch_end=27.8         # seconds
)
```

Becomes cut dictionaries with frame-based timing:
```python
{
    "cut_in": 252,         # frames (10.5 * 24fps)
    "cut_out": 607,        # frames (25.3 * 24fps)
    "cut_duration": 355,   # frames
    "link_id": 1
}
```

### Linking System
- Maintains the same Link Group ID system for DaVinci Resolve
- Each segment gets a unique link ID shared between video and audio clips
- Dub tracks get Transform effects for picture-in-picture display

## Improvements Made

### Issues Fixed:
1. **Missing Beginning/End Coverage**: Now creates complete segment maps that cover the entire video duration
2. **No Alignment Verification**: Added quality verification to detect alignment problems
3. **Missing Audio Parts**: Improved segment creation to handle edge cases better

### Enhanced Features:
- **Complete Coverage**: Automatically adds beginning and end segments if not covered by matches
- **Quality Verification**: Checks alignment quality and warns about potential issues
- **Better Matching**: Lowered thresholds and improved filtering for more matches
- **Detailed Debugging**: Added extensive logging and debug information
- **Fallback Handling**: Creates single segment if insufficient matches found

## Testing and Debugging

Use the test script to debug alignment issues:

```bash
# Test alignment between two files
python test_alignment.py "english_file.mkv" "dutch_file.mkv"

# With debug logging
python test_alignment.py "english_file.mkv" "dutch_file.mkv" --debug
```

The test script will show:
- Video durations and duration ratios
- Number of fingerprints generated
- Matching points found
- Segment mappings created
- Coverage percentages
- Quality verification results

## Troubleshooting

### "Insufficient matching points found"
**Causes:**
- Audio tracks are very different (different content, quality, etc.)
- Files have different audio characteristics
- One file has very low audio quality

**Solutions:**
- Check that both files contain similar content
- Verify audio quality in both files
- The system now automatically tries lower thresholds
- Will create single segment as fallback

### "Duration ratio seems unreasonable"
**Causes:**
- Files have very different durations
- Alignment failed to find proper matches
- One file is significantly sped up/slowed down

**Solutions:**
- Check if files are actually the same content
- Verify frame rates are similar
- Consider manual speed adjustment before alignment

### "Missing beginning of Dutch dub"
**Fixed:** Now automatically adds beginning segment if first match doesn't start at 0

### "Dutch dub doesn't seem aligned"
**Debugging:**
1. Run `test_alignment.py` to see detailed alignment info
2. Check matching points - should show reasonable time offsets
3. Verify segment mappings cover full duration
4. Look for quality verification warnings

### "Missing parts of audio track"
**Fixed:**
- Improved segment creation with better filtering
- Complete segment map ensures full coverage
- Reduced minimum segment duration threshold

## Advanced Configuration

You can modify these parameters in the code:

```python
# In audio_track_aligner.py
match_threshold = 0.7  # Lower = more matches, higher = better quality
min_segment_duration = 0.5  # Minimum segment length in seconds

# In main_aligner.py
min_segment_duration = 1.0  # For complete segment map creation
```
